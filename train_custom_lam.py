#!/usr/bin/env python3
"""
train_custom_lam.py

自定义LAM训练脚本，用于训练质量检测视频的Latent Action Model
"""

import os
import sys
import torch
import yaml
import argparse
from pathlib import Path
from lightning import Trainer
from lightning.pytorch.loggers import TensorBoardLogger
from lightning.pytorch.callbacks import ModelCheckpoint, EarlyStopping, LearningRateMonitor

# 添加UniVLA路径
sys.path.append('.')
sys.path.append('./UniVLA')

from custom_lam_dataset import CustomLAMDataModule
from UniVLA.latent_action_model.genie.model import DINO_LAM

class CustomLAMTrainer:
    """自定义LAM训练器"""
    
    def __init__(self, config_path: str):
        """
        初始化训练器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self.load_config()
        
    def load_config(self) -> dict:
        """加载配置文件"""
        with open(self.config_path, 'r') as f:
            config = yaml.safe_load(f)
        return config
    
    def setup_model(self) -> DINO_LAM:
        """设置LAM模型"""
        model_config = self.config['model']
        
        # 创建DINO_LAM模型
        model = DINO_LAM(
            image_channels=model_config['image_channels'],
            lam_model_dim=model_config['lam_model_dim'],
            lam_latent_dim=model_config['lam_latent_dim'],
            lam_num_latents=model_config['lam_num_latents'],
            lam_patch_size=model_config['lam_patch_size'],
            lam_enc_blocks=model_config['lam_enc_blocks'],
            lam_dec_blocks=model_config['lam_dec_blocks'],
            lam_num_heads=model_config['lam_num_heads'],
            vq_beta=model_config['vq_beta'],
            log_interval=model_config['log_interval'],
            log_path=model_config['log_path'],
            task_name=model_config['task_name'],
            stage=model_config['stage'],
            optimizer=torch.optim.AdamW,  # 直接传递类
            stage_one_ckpt=model_config.get('stage_one_ckpt', None)
        )
        
        return model
    
    def setup_data_module(self) -> CustomLAMDataModule:
        """设置数据模块"""
        data_config = self.config['data']
        
        data_module = CustomLAMDataModule(
            train_data_path=data_config['train_data_path'],
            val_data_path=data_config['val_data_path'],
            batch_size=data_config['batch_size'],
            num_workers=data_config['num_workers'],
            image_aug=data_config['image_aug']
        )
        
        return data_module
    
    def setup_callbacks(self) -> list:
        """设置回调函数"""
        callbacks = []
        
        # 模型检查点
        checkpoint_callback = ModelCheckpoint(
            dirpath=self.config['trainer']['callbacks'][0]['init_args']['dirpath'],
            filename=self.config['trainer']['callbacks'][0]['init_args']['filename'],
            verbose=self.config['trainer']['callbacks'][0]['init_args']['verbose'],
            save_last=self.config['trainer']['callbacks'][0]['init_args']['save_last'],
            save_top_k=self.config['trainer']['callbacks'][0]['init_args']['save_top_k'],
            monitor=self.config['trainer']['callbacks'][0]['init_args']['monitor'],
            mode=self.config['trainer']['callbacks'][0]['init_args']['mode'],
            every_n_train_steps=self.config['trainer']['callbacks'][0]['init_args']['every_n_train_steps']
        )
        callbacks.append(checkpoint_callback)
        
        # 早停
        early_stopping = EarlyStopping(
            monitor=self.config['trainer']['callbacks'][1]['init_args']['monitor'],
            patience=self.config['trainer']['callbacks'][1]['init_args']['patience'],
            mode=self.config['trainer']['callbacks'][1]['init_args']['mode'],
            verbose=self.config['trainer']['callbacks'][1]['init_args']['verbose']
        )
        callbacks.append(early_stopping)
        
        # 学习率监控
        lr_monitor = LearningRateMonitor(
            logging_interval=self.config['trainer']['callbacks'][2]['init_args']['logging_interval']
        )
        callbacks.append(lr_monitor)
        
        return callbacks
    
    def setup_logger(self):
        """设置日志记录器"""
        logger_config = self.config['logger'][0]['init_args']
        
        logger = TensorBoardLogger(
            save_dir=logger_config['save_dir'],
            name=logger_config['name'],
            version=logger_config['version'],
            default_hp_metric=logger_config['default_hp_metric']
        )
        
        return logger
    
    def train(self):
        """开始训练"""
        print("🚀 开始自定义LAM训练")
        print("=" * 50)
        
        # 检查数据文件是否存在
        data_config = self.config['data']
        if not os.path.exists(data_config['train_data_path']):
            raise FileNotFoundError(f"训练数据文件不存在: {data_config['train_data_path']}")
        if not os.path.exists(data_config['val_data_path']):
            raise FileNotFoundError(f"验证数据文件不存在: {data_config['val_data_path']}")
        
        # 设置模型
        print("📦 设置LAM模型...")
        model = self.setup_model()
        
        # 设置数据模块
        print("📊 设置数据模块...")
        data_module = self.setup_data_module()
        
        # 设置回调和日志
        print("⚙️ 设置训练组件...")
        callbacks = self.setup_callbacks()
        logger = self.setup_logger()
        
        # 创建训练器
        trainer_config = self.config['trainer']
        trainer = Trainer(
            max_epochs=trainer_config['max_epochs'],
            accelerator=trainer_config['accelerator'],
            devices=trainer_config['devices'],
            strategy=trainer_config['strategy'],
            precision=trainer_config['precision'],
            log_every_n_steps=trainer_config['log_every_n_steps'],
            gradient_clip_val=trainer_config['gradient_clip_val'],
            callbacks=callbacks,
            logger=logger,
            enable_progress_bar=True,
            enable_model_summary=True
        )
        
        # 开始训练
        print("🎯 开始训练...")
        trainer.fit(model, data_module)
        
        print("✅ 训练完成!")
        
        # 保存最终模型
        final_model_path = os.path.join(
            self.config['trainer']['callbacks'][0]['init_args']['dirpath'],
            'final_model.ckpt'
        )
        trainer.save_checkpoint(final_model_path)
        print(f"💾 最终模型已保存到: {final_model_path}")

def main():
    parser = argparse.ArgumentParser(description='Train Custom LAM for Quality Inspection')
    parser.add_argument('--config', type=str, default='config_custom_lam.yaml',
                       help='Path to configuration file')
    parser.add_argument('--check_data', action='store_true',
                       help='Check if preprocessed data exists')
    
    args = parser.parse_args()
    
    # 检查数据
    if args.check_data:
        print("🔍 检查预处理数据...")
        if os.path.exists('processed_data/train_data.pkl'):
            print("✅ 训练数据存在")
        else:
            print("❌ 训练数据不存在，请先运行数据预处理")
            
        if os.path.exists('processed_data/val_data.pkl'):
            print("✅ 验证数据存在")
        else:
            print("❌ 验证数据不存在，请先运行数据预处理")
        return
    
    # 检查配置文件
    if not os.path.exists(args.config):
        print(f"❌ 配置文件不存在: {args.config}")
        return
    
    # 创建训练器并开始训练
    trainer = CustomLAMTrainer(args.config)
    trainer.train()

if __name__ == "__main__":
    main()
