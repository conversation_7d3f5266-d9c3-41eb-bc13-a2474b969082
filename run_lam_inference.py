#!/usr/bin/env python3
"""
run_lam_inference.py

运行LAM推理的完整流水线脚本
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def run_command(command: str, description: str = ""):
    """运行命令并处理错误"""
    if description:
        print(f"🔄 {description}")
    
    print(f"执行命令: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"❌ 命令执行失败: {command}")
        print(f"错误输出: {result.stderr}")
        return False
    else:
        print(f"✅ {description} 完成")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True

def check_prerequisites():
    """检查先决条件"""
    print("🔍 检查先决条件...")
    
    # 检查预处理数据
    if not os.path.exists('processed_data/train_data.pkl'):
        print("❌ 训练数据不存在，请先运行数据预处理")
        return False
    
    if not os.path.exists('processed_data/val_data.pkl'):
        print("❌ 验证数据不存在，请先运行数据预处理")
        return False
    
    print("✅ 预处理数据存在")
    
    # 检查UniVLA代码库
    if not os.path.exists('UniVLA'):
        print("❌ UniVLA代码库不存在")
        return False
    
    print("✅ UniVLA代码库存在")
    
    # 检查Python依赖
    try:
        import torch
        import torchvision
        import matplotlib
        import seaborn
        import sklearn
        from huggingface_hub import snapshot_download
        print("✅ 主要Python依赖已安装")
    except ImportError as e:
        print(f"❌ 缺少Python依赖: {e}")
        print("请在conda环境中安装: pip install matplotlib seaborn scikit-learn huggingface_hub")
        return False
    
    return True

def run_inference(args):
    """运行LAM推理"""
    print("\n" + "="*50)
    print("🚀 步骤 1: LAM模型推理")
    print("="*50)

    # 构建推理命令 - 直接运行python，不需要conda activate
    cmd_parts = [
        "python inference_lam.py",
        f"--data_path {args.data_path}",
        f"--output_dir {args.output_dir}",
        f"--max_samples {args.max_samples}"
    ]

    if args.save_visualizations:
        cmd_parts.append("--save_visualizations")

    cmd = " ".join(cmd_parts)

    if not run_command(cmd, "LAM推理"):
        return False

    return True

def run_analysis(args):
    """运行结果分析"""
    print("\n" + "="*50)
    print("📊 步骤 2: 结果分析")
    print("="*50)

    cmd = f"python analyze_lam_results.py --results_dir {args.output_dir}"

    if not run_command(cmd, "结果分析"):
        return False

    return True

def show_results_summary(output_dir):
    """显示结果摘要"""
    print("\n" + "="*50)
    print("📋 推理结果摘要")
    print("="*50)
    
    output_path = Path(output_dir)
    
    # 检查生成的文件
    files_to_check = [
        ('inference_summary.pkl', '推理汇总数据'),
        ('analysis_report.txt', '分析报告'),
        ('task_distribution.png', '任务分布图'),
        ('codebook_analysis.png', 'Codebook分析图'),
        ('latent_space_analysis.png', '潜在空间分析图')
    ]
    
    print("生成的文件:")
    for filename, description in files_to_check:
        file_path = output_path / filename
        if file_path.exists():
            print(f"  ✅ {filename} - {description}")
        else:
            print(f"  ❌ {filename} - {description} (未找到)")
    
    # 统计潜在编码文件
    latent_files = list(output_path.glob('*_latent_codes.pkl'))
    print(f"  📊 {len(latent_files)} 个潜在编码文件")
    
    # 统计可视化文件
    viz_files = list(output_path.glob('*_visualization.png'))
    print(f"  🖼️ {len(viz_files)} 个可视化文件")
    
    print(f"\n📁 所有结果保存在: {output_path.absolute()}")
    
    # 显示如何查看结果
    print(f"\n💡 查看结果的方法:")
    print(f"  1. 查看分析报告: cat {output_path}/analysis_report.txt")
    print(f"  2. 查看分析图表: 打开 {output_path}/ 目录中的PNG文件")
    print(f"  3. 加载潜在编码: 使用pickle加载 *_latent_codes.pkl 文件")

def main():
    parser = argparse.ArgumentParser(description='Run LAM Inference Pipeline')
    parser.add_argument('--data_path', type=str, default='processed_data/train_data.pkl',
                       help='预处理数据文件路径')
    parser.add_argument('--output_dir', type=str, default='inference_results',
                       help='输出目录')
    parser.add_argument('--max_samples', type=int, default=20,
                       help='最大处理样本数')
    parser.add_argument('--save_visualizations', action='store_true',
                       help='保存可视化结果')
    parser.add_argument('--skip_inference', action='store_true',
                       help='跳过推理步骤（仅运行分析）')
    parser.add_argument('--skip_analysis', action='store_true',
                       help='跳过分析步骤（仅运行推理）')
    parser.add_argument('--check_only', action='store_true',
                       help='仅检查先决条件')
    
    args = parser.parse_args()
    
    print("🌟 UniVLA Latent Action Model 推理流水线")
    print("=" * 60)
    
    # 检查先决条件
    if not check_prerequisites():
        print("❌ 先决条件检查失败，请解决上述问题后重试")
        return
    
    if args.check_only:
        print("✅ 先决条件检查通过")
        return
    
    # 步骤1: 运行推理
    if not args.skip_inference:
        if not run_inference(args):
            print("❌ LAM推理失败")
            return
    else:
        print("⏭️ 跳过推理步骤")
    
    # 步骤2: 运行分析
    if not args.skip_analysis:
        if not run_analysis(args):
            print("❌ 结果分析失败")
            return
    else:
        print("⏭️ 跳过分析步骤")
    
    # 显示结果摘要
    show_results_summary(args.output_dir)
    
    print("\n" + "="*60)
    print("🎉 LAM推理流水线完成!")
    print("="*60)
    print("\n📋 后续步骤:")
    print("1. 查看生成的分析报告和图表")
    print("2. 使用潜在动作编码训练VLA模型")
    print("3. 在下游任务上评估模型性能")

if __name__ == "__main__":
    main()
