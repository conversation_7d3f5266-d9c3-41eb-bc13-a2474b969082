#!/usr/bin/env python3
"""
analyze_lam_results.py

分析LAM推理结果的脚本
"""

import os
import pickle
import argparse
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from collections import Counter
import pandas as pd

class LAMResultAnalyzer:
    """LAM结果分析器"""
    
    def __init__(self, results_dir):
        self.results_dir = Path(results_dir)
        self.summary_data = self.load_summary()
        self.latent_data = self.load_latent_codes()
        
    def load_summary(self):
        """加载汇总数据"""
        summary_path = self.results_dir / 'inference_summary.pkl'
        if not summary_path.exists():
            print(f"❌ 汇总文件不存在: {summary_path}")
            return None
            
        with open(summary_path, 'rb') as f:
            data = pickle.load(f)
        
        print(f"📊 加载汇总数据: {len(data)} 个样本")
        return data
    
    def load_latent_codes(self):
        """加载所有潜在编码数据"""
        latent_files = list(self.results_dir.glob('*_latent_codes.pkl'))
        latent_data = []
        
        for file_path in latent_files:
            with open(file_path, 'rb') as f:
                data = pickle.load(f)
                latent_data.append(data)
        
        print(f"📊 加载潜在编码数据: {len(latent_data)} 个文件")
        return latent_data
    
    def analyze_task_distribution(self):
        """分析任务分布"""
        if not self.summary_data:
            return
            
        tasks = [item['task_instruction'] for item in self.summary_data]
        task_counts = Counter(tasks)
        
        print("\n📈 任务分布分析:")
        print("-" * 40)
        for task, count in task_counts.items():
            percentage = (count / len(tasks)) * 100
            print(f"{task}: {count} ({percentage:.1f}%)")
        
        # 绘制任务分布图
        plt.figure(figsize=(12, 6))
        tasks_list = list(task_counts.keys())
        counts_list = list(task_counts.values())
        
        plt.bar(range(len(tasks_list)), counts_list)
        plt.xlabel('Task Type')
        plt.ylabel('Count')
        plt.title('Task Distribution')
        plt.xticks(range(len(tasks_list)), [t.replace(' ', '\n') for t in tasks_list], rotation=45)
        plt.tight_layout()
        
        save_path = self.results_dir / 'task_distribution.png'
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"💾 任务分布图保存至: {save_path}")
    
    def analyze_codebook_usage(self):
        """分析codebook使用情况"""
        if not self.latent_data:
            return
            
        all_indices = []
        for data in self.latent_data:
            indices = data['indices'].flatten()
            all_indices.extend(indices)
        
        # 统计每个codebook索引的使用频率
        index_counts = Counter(all_indices)
        total_codes = len(set(all_indices))
        max_index = max(all_indices) if all_indices else 0
        
        print(f"\n📊 Codebook使用分析:")
        print("-" * 40)
        print(f"使用的编码数量: {total_codes}")
        print(f"最大编码索引: {max_index}")
        print(f"总编码使用次数: {len(all_indices)}")
        
        # 计算使用频率
        usage_freq = np.array(list(index_counts.values()))
        print(f"平均使用频率: {usage_freq.mean():.2f}")
        print(f"使用频率标准差: {usage_freq.std():.2f}")
        
        # 绘制codebook使用分布
        plt.figure(figsize=(15, 5))
        
        # 子图1: 使用频率直方图
        plt.subplot(1, 3, 1)
        plt.hist(usage_freq, bins=20, alpha=0.7)
        plt.xlabel('Usage Frequency')
        plt.ylabel('Number of Codes')
        plt.title('Codebook Usage Frequency Distribution')
        
        # 子图2: 编码索引使用情况
        plt.subplot(1, 3, 2)
        indices = sorted(index_counts.keys())
        frequencies = [index_counts[i] for i in indices]
        plt.bar(indices, frequencies, alpha=0.7)
        plt.xlabel('Codebook Index')
        plt.ylabel('Usage Count')
        plt.title('Usage by Codebook Index')
        
        # 子图3: 累积使用分布
        plt.subplot(1, 3, 3)
        sorted_freq = sorted(usage_freq, reverse=True)
        cumsum_freq = np.cumsum(sorted_freq)
        plt.plot(range(len(sorted_freq)), cumsum_freq / cumsum_freq[-1] * 100)
        plt.xlabel('Codebook Index (sorted by usage)')
        plt.ylabel('Cumulative Usage (%)')
        plt.title('Cumulative Codebook Usage')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        save_path = self.results_dir / 'codebook_analysis.png'
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"💾 Codebook分析图保存至: {save_path}")
    
    def analyze_latent_space(self):
        """分析潜在空间"""
        if not self.latent_data:
            return
            
        # 收集所有潜在编码
        all_latents = []
        all_tasks = []
        
        for data in self.latent_data:
            latent_codes = data['latent_codes']  # (T-1, num_codes, latent_dim)
            task = data['task_instruction']
            
            # 展平潜在编码
            flattened = latent_codes.reshape(-1, latent_codes.shape[-1])
            all_latents.append(flattened)
            all_tasks.extend([task] * len(flattened))
        
        if not all_latents:
            return
            
        # 合并所有数据
        latent_matrix = np.vstack(all_latents)
        
        print(f"\n🧠 潜在空间分析:")
        print("-" * 40)
        print(f"潜在编码形状: {latent_matrix.shape}")
        print(f"潜在维度: {latent_matrix.shape[1]}")
        print(f"编码数量: {latent_matrix.shape[0]}")
        
        # 统计信息
        print(f"均值: {latent_matrix.mean():.4f}")
        print(f"标准差: {latent_matrix.std():.4f}")
        print(f"最小值: {latent_matrix.min():.4f}")
        print(f"最大值: {latent_matrix.max():.4f}")
        
        # 绘制潜在空间分析图
        plt.figure(figsize=(15, 10))
        
        # 子图1: 潜在编码分布
        plt.subplot(2, 3, 1)
        plt.hist(latent_matrix.flatten(), bins=50, alpha=0.7)
        plt.xlabel('Latent Value')
        plt.ylabel('Frequency')
        plt.title('Latent Code Value Distribution')
        
        # 子图2: 每个维度的均值
        plt.subplot(2, 3, 2)
        dim_means = latent_matrix.mean(axis=0)
        plt.plot(dim_means)
        plt.xlabel('Latent Dimension')
        plt.ylabel('Mean Value')
        plt.title('Mean Value per Dimension')
        plt.grid(True, alpha=0.3)
        
        # 子图3: 每个维度的标准差
        plt.subplot(2, 3, 3)
        dim_stds = latent_matrix.std(axis=0)
        plt.plot(dim_stds)
        plt.xlabel('Latent Dimension')
        plt.ylabel('Standard Deviation')
        plt.title('Std Dev per Dimension')
        plt.grid(True, alpha=0.3)
        
        # 子图4: 协方差矩阵热图（采样部分维度）
        plt.subplot(2, 3, 4)
        sample_dims = min(32, latent_matrix.shape[1])
        sample_matrix = latent_matrix[:, :sample_dims]
        corr_matrix = np.corrcoef(sample_matrix.T)
        sns.heatmap(corr_matrix, cmap='coolwarm', center=0, square=True)
        plt.title(f'Correlation Matrix (first {sample_dims} dims)')
        
        # 子图5: PCA分析（如果有足够的数据）
        plt.subplot(2, 3, 5)
        if latent_matrix.shape[0] > 50:
            from sklearn.decomposition import PCA
            pca = PCA(n_components=min(10, latent_matrix.shape[1]))
            pca.fit(latent_matrix)
            plt.plot(range(1, len(pca.explained_variance_ratio_) + 1), 
                    pca.explained_variance_ratio_, 'bo-')
            plt.xlabel('Principal Component')
            plt.ylabel('Explained Variance Ratio')
            plt.title('PCA Explained Variance')
            plt.grid(True, alpha=0.3)
        
        # 子图6: 任务类型的潜在编码分布
        plt.subplot(2, 3, 6)
        unique_tasks = list(set(all_tasks))
        if len(unique_tasks) > 1:
            task_means = []
            for task in unique_tasks:
                task_indices = [i for i, t in enumerate(all_tasks) if t == task]
                task_latents = latent_matrix[task_indices]
                task_means.append(task_latents.mean())
            
            plt.bar(range(len(unique_tasks)), task_means)
            plt.xlabel('Task Type')
            plt.ylabel('Mean Latent Value')
            plt.title('Mean Latent Value by Task')
            plt.xticks(range(len(unique_tasks)), 
                      [t.split()[-1] for t in unique_tasks], rotation=45)
        
        plt.tight_layout()
        save_path = self.results_dir / 'latent_space_analysis.png'
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"💾 潜在空间分析图保存至: {save_path}")
    
    def generate_report(self):
        """生成分析报告"""
        report_path = self.results_dir / 'analysis_report.txt'
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("UniVLA Latent Action Model 推理结果分析报告\n")
            f.write("=" * 50 + "\n\n")
            
            if self.summary_data:
                f.write(f"总样本数: {len(self.summary_data)}\n")
                
                # 任务分布
                tasks = [item['task_instruction'] for item in self.summary_data]
                task_counts = Counter(tasks)
                f.write(f"\n任务分布:\n")
                for task, count in task_counts.items():
                    percentage = (count / len(tasks)) * 100
                    f.write(f"  - {task}: {count} ({percentage:.1f}%)\n")
            
            if self.latent_data:
                f.write(f"\n潜在编码文件数: {len(self.latent_data)}\n")
                
                # 编码统计
                sample_data = self.latent_data[0]
                f.write(f"编码形状: {sample_data['shape']}\n")
                f.write(f"潜在维度: {sample_data['latent_codes'].shape[-1]}\n")
                
                # Codebook使用统计
                all_indices = []
                for data in self.latent_data:
                    indices = data['indices'].flatten()
                    all_indices.extend(indices)
                
                unique_codes = len(set(all_indices))
                f.write(f"使用的唯一编码数: {unique_codes}\n")
                f.write(f"总编码使用次数: {len(all_indices)}\n")
            
            f.write(f"\n生成的分析图表:\n")
            f.write(f"  - task_distribution.png: 任务分布图\n")
            f.write(f"  - codebook_analysis.png: Codebook使用分析\n")
            f.write(f"  - latent_space_analysis.png: 潜在空间分析\n")
        
        print(f"📋 分析报告保存至: {report_path}")
    
    def run_analysis(self):
        """运行完整分析"""
        print("🔍 开始分析LAM推理结果")
        print("=" * 50)
        
        self.analyze_task_distribution()
        self.analyze_codebook_usage()
        self.analyze_latent_space()
        self.generate_report()
        
        print(f"\n✅ 分析完成! 结果保存在 {self.results_dir} 目录中")

def main():
    parser = argparse.ArgumentParser(description='Analyze LAM Inference Results')
    parser.add_argument('--results_dir', type=str, default='inference_results',
                       help='推理结果目录')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.results_dir):
        print(f"❌ 结果目录不存在: {args.results_dir}")
        return
    
    analyzer = LAMResultAnalyzer(args.results_dir)
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
