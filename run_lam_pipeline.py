#!/usr/bin/env python3
"""
run_lam_pipeline.py

完整的LAM训练流水线脚本，包含数据预处理、训练和验证
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def run_command(command: str, description: str = ""):
    """运行命令并处理错误"""
    if description:
        print(f"🔄 {description}")
    
    print(f"执行命令: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"❌ 命令执行失败: {command}")
        print(f"错误输出: {result.stderr}")
        return False
    else:
        print(f"✅ {description} 完成")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True

def check_prerequisites():
    """检查先决条件"""
    print("🔍 检查先决条件...")
    
    # 检查数据目录
    if not os.path.exists('data'):
        print("❌ 数据目录 'data' 不存在")
        return False
    
    # 检查视频文件
    video_count = len(list(Path('data').rglob('*.mp4')))
    if video_count == 0:
        print("❌ 在数据目录中未找到MP4文件")
        return False
    
    print(f"✅ 找到 {video_count} 个视频文件")
    
    # 检查UniVLA代码库
    if not os.path.exists('UniVLA'):
        print("❌ UniVLA代码库不存在，请确保已克隆到当前目录")
        return False
    
    print("✅ UniVLA代码库存在")
    
    # 检查Python依赖
    try:
        import torch
        import torchvision
        import lightning
        import cv2
        import numpy as np
        from PIL import Image
        print("✅ 主要Python依赖已安装")
    except ImportError as e:
        print(f"❌ 缺少Python依赖: {e}")
        return False
    
    return True

def preprocess_data(args):
    """数据预处理步骤"""
    print("\n" + "="*50)
    print("📊 步骤 1: 数据预处理")
    print("="*50)
    
    # 检查是否已有预处理数据
    if os.path.exists('processed_data/train_data.pkl') and os.path.exists('processed_data/val_data.pkl'):
        if not args.force_preprocess:
            print("✅ 预处理数据已存在，跳过预处理步骤")
            print("   如需重新预处理，请使用 --force_preprocess 参数")
            return True
        else:
            print("🔄 强制重新预处理数据...")
    
    # 运行数据预处理
    cmd = f"python preprocess_videos_for_lam.py --data_dir data --output_dir processed_data --train_ratio {args.train_ratio}"
    
    if not run_command(cmd, "数据预处理"):
        return False
    
    # 验证预处理结果
    if os.path.exists('processed_data/train_data.pkl') and os.path.exists('processed_data/val_data.pkl'):
        print("✅ 数据预处理完成")
        return True
    else:
        print("❌ 数据预处理失败")
        return False

def test_dataset(args):
    """测试数据集加载"""
    print("\n" + "="*50)
    print("🧪 步骤 2: 测试数据集加载")
    print("="*50)
    
    cmd = "python custom_lam_dataset.py --data_path processed_data/train_data.pkl"
    
    if not run_command(cmd, "测试数据集加载"):
        return False
    
    return True

def train_model(args):
    """训练LAM模型"""
    print("\n" + "="*50)
    print("🚀 步骤 3: 训练LAM模型")
    print("="*50)
    
    # 检查配置文件
    config_file = args.config or 'config_custom_lam.yaml'
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    # 运行训练
    cmd = f"python train_custom_lam.py --config {config_file}"
    
    if not run_command(cmd, "LAM模型训练"):
        return False
    
    return True

def validate_results():
    """验证训练结果"""
    print("\n" + "="*50)
    print("✅ 步骤 4: 验证训练结果")
    print("="*50)
    
    # 检查日志目录
    if os.path.exists('logs'):
        print("✅ 训练日志目录存在")
        
        # 列出检查点文件
        checkpoint_files = list(Path('logs').rglob('*.ckpt'))
        if checkpoint_files:
            print(f"✅ 找到 {len(checkpoint_files)} 个检查点文件:")
            for ckpt in checkpoint_files[-3:]:  # 显示最后3个
                print(f"   - {ckpt}")
        else:
            print("⚠️ 未找到检查点文件")
    else:
        print("❌ 训练日志目录不存在")
        return False
    
    # 检查TensorBoard日志
    tb_logs = list(Path('logs').rglob('events.out.tfevents.*'))
    if tb_logs:
        print(f"✅ 找到 {len(tb_logs)} 个TensorBoard日志文件")
        print("💡 可以使用以下命令查看训练日志:")
        print("   tensorboard --logdir logs")
    else:
        print("⚠️ 未找到TensorBoard日志文件")
    
    return True

def main():
    parser = argparse.ArgumentParser(description='UniVLA LAM Training Pipeline')
    parser.add_argument('--skip_preprocess', action='store_true',
                       help='跳过数据预处理步骤')
    parser.add_argument('--force_preprocess', action='store_true',
                       help='强制重新预处理数据')
    parser.add_argument('--skip_test', action='store_true',
                       help='跳过数据集测试步骤')
    parser.add_argument('--skip_train', action='store_true',
                       help='跳过训练步骤')
    parser.add_argument('--train_ratio', type=float, default=0.8,
                       help='训练数据比例')
    parser.add_argument('--config', type=str, default='config_custom_lam.yaml',
                       help='训练配置文件路径')
    parser.add_argument('--check_only', action='store_true',
                       help='仅检查先决条件')
    
    args = parser.parse_args()
    
    print("🌟 UniVLA Latent Action Model 训练流水线")
    print("=" * 60)
    
    # 检查先决条件
    if not check_prerequisites():
        print("❌ 先决条件检查失败，请解决上述问题后重试")
        return
    
    if args.check_only:
        print("✅ 先决条件检查通过")
        return
    
    # 步骤1: 数据预处理
    if not args.skip_preprocess:
        if not preprocess_data(args):
            print("❌ 数据预处理失败")
            return
    else:
        print("⏭️ 跳过数据预处理步骤")
    
    # 步骤2: 测试数据集
    if not args.skip_test:
        if not test_dataset(args):
            print("❌ 数据集测试失败")
            return
    else:
        print("⏭️ 跳过数据集测试步骤")
    
    # 步骤3: 训练模型
    if not args.skip_train:
        if not train_model(args):
            print("❌ 模型训练失败")
            return
    else:
        print("⏭️ 跳过模型训练步骤")
    
    # 步骤4: 验证结果
    validate_results()
    
    print("\n" + "="*60)
    print("🎉 LAM训练流水线完成!")
    print("="*60)
    print("\n📋 后续步骤:")
    print("1. 查看训练日志: tensorboard --logdir logs")
    print("2. 使用训练好的LAM模型进行VLA预训练")
    print("3. 在下游任务上微调VLA模型")

if __name__ == "__main__":
    main()
