#!/usr/bin/env python3
"""
custom_lam_dataset.py

自定义数据集类，用于加载预处理后的视频数据并适配UniVLA Latent Action Model的训练需求。
"""

import os
import pickle
import torch
import numpy as np
from torch.utils.data import Dataset, DataLoader
from typing import Dict, List, Optional, Tuple
import torchvision.transforms as transforms
from lightning import LightningDataModule
import json

class CustomLAMDataset(Dataset):
    """
    自定义LAM数据集类
    
    数据格式要求：
    - videos: (T=2, C=3, H=224, W=224) - 视频序列张量
    - task_instruction: str - 任务指令文本
    """
    
    def __init__(
        self, 
        data_path: str,
        transform: Optional[transforms.Compose] = None,
        augment: bool = False
    ):
        """
        初始化数据集
        
        Args:
            data_path: 预处理数据文件路径 (.pkl)
            transform: 图像变换
            augment: 是否启用数据增强
        """
        self.data_path = data_path
        self.augment = augment
        
        # 加载数据
        with open(data_path, 'rb') as f:
            self.samples = pickle.load(f)
        
        print(f"Loaded {len(self.samples)} samples from {data_path}")
        
        # 设置默认变换
        if transform is None:
            self.transform = transforms.Compose([
                transforms.Normalize(
                    mean=[0.485, 0.456, 0.406],  # ImageNet标准化
                    std=[0.229, 0.224, 0.225]
                )
            ])
        else:
            self.transform = transform
        
        # 数据增强变换
        if self.augment:
            self.augment_transform = transforms.Compose([
                transforms.ColorJitter(brightness=0.1, contrast=0.1, saturation=0.1, hue=0.05),
                transforms.RandomApply([transforms.GaussianBlur(kernel_size=3)], p=0.1),
            ])
        else:
            self.augment_transform = None
    
    def __len__(self) -> int:
        return len(self.samples)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """
        获取单个数据样本
        
        Returns:
            Dict包含:
            - videos: (T=2, C=3, H=224, W=224)
            - task_instruction: str
        """
        sample = self.samples[idx]
        
        # 获取视频张量
        videos = sample['videos']  # Shape: (T=2, C=3, H=224, W=224)
        
        # 应用变换
        if self.transform:
            # 对每一帧应用变换
            transformed_frames = []
            for t in range(videos.shape[0]):
                frame = videos[t]  # (C, H, W)
                
                # 数据增强（仅训练时）
                if self.augment and self.augment_transform:
                    frame = self.augment_transform(frame)
                
                # 标准化
                frame = self.transform(frame)
                transformed_frames.append(frame)
            
            videos = torch.stack(transformed_frames, dim=0)
        
        # 返回LAM期望的格式
        return {
            'videos': videos,  # (T=2, C=3, H=224, W=224)
            'task_instruction': sample['task_instruction'],
            'dataset_names': sample.get('dataset_name', 'custom'),
            'video_path': sample.get('video_path', ''),
        }

class CustomLAMDataModule(LightningDataModule):
    """
    Lightning数据模块，用于LAM训练
    """
    
    def __init__(
        self,
        train_data_path: str,
        val_data_path: str,
        batch_size: int = 16,
        num_workers: int = 4,
        image_aug: bool = True,
        **kwargs
    ):
        super().__init__()
        self.train_data_path = train_data_path
        self.val_data_path = val_data_path
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.image_aug = image_aug
        
        # 保存超参数
        self.save_hyperparameters()
    
    def setup(self, stage: str) -> None:
        """设置数据集"""
        if stage == "fit":
            self.train_dataset = CustomLAMDataset(
                self.train_data_path,
                augment=self.image_aug
            )
            self.val_dataset = CustomLAMDataset(
                self.val_data_path,
                augment=False
            )
        elif stage == "test":
            self.test_dataset = CustomLAMDataset(
                self.val_data_path,
                augment=False
            )
    
    def train_dataloader(self) -> DataLoader:
        return DataLoader(
            self.train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=self.num_workers,
            collate_fn=self.collate_fn,
            pin_memory=True
        )
    
    def val_dataloader(self) -> DataLoader:
        return DataLoader(
            self.val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            collate_fn=self.collate_fn,
            pin_memory=True
        )
    
    def test_dataloader(self) -> DataLoader:
        return DataLoader(
            self.test_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            collate_fn=self.collate_fn,
            pin_memory=True
        )
    
    def collate_fn(self, batch: List[Dict]) -> Dict[str, torch.Tensor]:
        """
        自定义collate函数，用于批处理数据
        """
        # 提取各个字段
        videos = torch.stack([item['videos'] for item in batch], dim=0)  # (B, T=2, C, H, W)
        task_instructions = [item['task_instruction'] for item in batch]
        dataset_names = [item['dataset_names'] for item in batch]
        video_paths = [item['video_path'] for item in batch]
        
        return {
            'videos': videos,
            'task_instruction': task_instructions,
            'dataset_names': dataset_names,
            'video_paths': video_paths
        }

def create_data_loaders(
    train_data_path: str,
    val_data_path: str,
    batch_size: int = 16,
    num_workers: int = 4,
    image_aug: bool = True
) -> Tuple[DataLoader, DataLoader]:
    """
    创建训练和验证数据加载器
    
    Args:
        train_data_path: 训练数据路径
        val_data_path: 验证数据路径
        batch_size: 批大小
        num_workers: 工作进程数
        image_aug: 是否启用图像增强
    
    Returns:
        (train_loader, val_loader)
    """
    # 创建数据集
    train_dataset = CustomLAMDataset(train_data_path, augment=image_aug)
    val_dataset = CustomLAMDataset(val_data_path, augment=False)
    
    # 创建collate函数
    def collate_fn(batch: List[Dict]) -> Dict[str, torch.Tensor]:
        videos = torch.stack([item['videos'] for item in batch], dim=0)
        task_instructions = [item['task_instruction'] for item in batch]
        dataset_names = [item['dataset_names'] for item in batch]
        
        return {
            'videos': videos,
            'task_instruction': task_instructions,
            'dataset_names': dataset_names
        }
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        collate_fn=collate_fn,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        collate_fn=collate_fn,
        pin_memory=True
    )
    
    return train_loader, val_loader

def test_dataset_loading(data_path: str):
    """测试数据集加载"""
    print(f"Testing dataset loading from {data_path}")

    # 创建数据集
    dataset = CustomLAMDataset(data_path)

    # 测试单个样本
    sample = dataset[0]
    print(f"Sample keys: {sample.keys()}")
    print(f"Videos shape: {sample['videos'].shape}")
    print(f"Task instruction: {sample['task_instruction']}")
    print(f"Dataset name: {sample['dataset_names']}")

    # 定义collate函数
    def collate_fn(batch: List[Dict]) -> Dict[str, torch.Tensor]:
        videos = torch.stack([item['videos'] for item in batch], dim=0)
        task_instructions = [item['task_instruction'] for item in batch]
        dataset_names = [item['dataset_names'] for item in batch]

        return {
            'videos': videos,
            'task_instruction': task_instructions,
            'dataset_names': dataset_names
        }

    # 创建数据加载器测试批处理
    loader = DataLoader(dataset, batch_size=2, collate_fn=collate_fn)
    batch = next(iter(loader))

    print(f"\nBatch test:")
    print(f"Batch videos shape: {batch['videos'].shape}")
    print(f"Batch task instructions: {len(batch['task_instruction'])}")
    print(f"First instruction: {batch['task_instruction'][0]}")

if __name__ == "__main__":
    # 测试数据集
    import argparse
    
    parser = argparse.ArgumentParser()
    parser.add_argument('--data_path', type=str, required=True,
                       help='Path to preprocessed data file')
    args = parser.parse_args()
    
    test_dataset_loading(args.data_path)
