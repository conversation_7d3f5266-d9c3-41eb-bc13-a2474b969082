#!/usr/bin/env python3
"""
preprocess_videos_for_lam.py

数据预处理脚本，将MP4视频转换为符合UniVLA Latent Action Model输入要求的格式。

基于UniVLA LAM的要求：
- 输入格式：视频序列 (B, T, C, H, W)，其中T=2（初始帧+目标帧）
- 分辨率：224x224像素（DINOv2要求）
- 数据格式：包含videos、task_instruction等字段
- 支持任务分类：ok、nok_apperence、nok_ele
"""

import os
import json
import cv2
import torch
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import torchvision.transforms as transforms
from PIL import Image
import argparse
from tqdm import tqdm
import pickle

class VideoPreprocessor:
    """视频预处理器，用于将MP4视频转换为LAM输入格式"""
    
    def __init__(self, target_resolution: Tuple[int, int] = (224, 224)):
        self.target_resolution = target_resolution
        self.transform = transforms.Compose([
            transforms.Resize(target_resolution),
            transforms.ToTensor(),
        ])
        
        # 任务描述映射
        self.task_descriptions = {
            'segmented_videos_ok': 'perform successful quality inspection',
            'segmented_videos_nok_apperence': 'detect appearance defects during inspection',
            'segmented_videos_nok_ele': 'detect electrical defects during inspection'
        }
    
    def extract_frames_from_video(self, video_path: str) -> List[np.ndarray]:
        """从视频中提取所有帧"""
        cap = cv2.VideoCapture(video_path)
        frames = []
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            # 转换BGR到RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            frames.append(frame_rgb)
        
        cap.release()
        return frames
    
    def select_frame_pair(self, frames: List[np.ndarray]) -> Tuple[np.ndarray, np.ndarray]:
        """
        选择帧对：初始帧和目标帧
        对于LAM训练，我们需要T=2的视频序列
        """
        if len(frames) < 2:
            # 如果只有一帧，复制该帧
            return frames[0], frames[0]
        
        # 选择第一帧作为初始帧，最后一帧作为目标帧
        initial_frame = frames[0]
        target_frame = frames[-1]
        
        return initial_frame, target_frame
    
    def preprocess_frame(self, frame: np.ndarray) -> torch.Tensor:
        """预处理单个帧"""
        # 转换为PIL Image
        pil_image = Image.fromarray(frame)
        # 应用变换
        tensor = self.transform(pil_image)
        return tensor
    
    def create_video_tensor(self, initial_frame: np.ndarray, target_frame: np.ndarray) -> torch.Tensor:
        """创建视频张量 (T, C, H, W)"""
        initial_tensor = self.preprocess_frame(initial_frame)
        target_tensor = self.preprocess_frame(target_frame)
        
        # 堆叠为 (T=2, C, H, W)
        video_tensor = torch.stack([initial_tensor, target_tensor], dim=0)
        return video_tensor
    
    def get_task_instruction(self, video_path: str) -> str:
        """根据视频路径确定任务指令"""
        path_parts = Path(video_path).parts
        
        for part in path_parts:
            if part in self.task_descriptions:
                return self.task_descriptions[part]
        
        # 默认任务描述
        return "perform quality inspection task"
    
    def process_single_video(self, video_path: str) -> Dict:
        """处理单个视频文件"""
        try:
            # 提取帧
            frames = self.extract_frames_from_video(video_path)
            if not frames:
                raise ValueError(f"No frames extracted from {video_path}")
            
            # 选择帧对
            initial_frame, target_frame = self.select_frame_pair(frames)
            
            # 创建视频张量
            video_tensor = self.create_video_tensor(initial_frame, target_frame)
            
            # 获取任务指令
            task_instruction = self.get_task_instruction(video_path)
            
            # 创建数据样本
            sample = {
                'videos': video_tensor,  # Shape: (T=2, C=3, H=224, W=224)
                'task_instruction': task_instruction,
                'video_path': video_path,
                'num_frames': len(frames),
                'dataset_name': self._get_dataset_name(video_path)
            }
            
            return sample
            
        except Exception as e:
            print(f"Error processing {video_path}: {str(e)}")
            return None
    
    def _get_dataset_name(self, video_path: str) -> str:
        """从视频路径提取数据集名称"""
        path_parts = Path(video_path).parts
        for part in path_parts:
            if 'segmented_videos' in part:
                return part
        return 'unknown'

def collect_video_files(data_dir: str) -> List[str]:
    """收集所有MP4视频文件"""
    video_files = []
    data_path = Path(data_dir)
    
    for video_file in data_path.rglob("*.mp4"):
        video_files.append(str(video_file))
    
    return sorted(video_files)

def create_dataset_split(video_files: List[str], train_ratio: float = 0.8) -> Tuple[List[str], List[str]]:
    """创建训练/验证数据集分割"""
    np.random.seed(42)  # 确保可重现性
    np.random.shuffle(video_files)
    
    split_idx = int(len(video_files) * train_ratio)
    train_files = video_files[:split_idx]
    val_files = video_files[split_idx:]
    
    return train_files, val_files

def save_processed_data(samples: List[Dict], output_path: str):
    """保存处理后的数据"""
    # 创建输出目录
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 保存为pickle文件（更适合torch tensor）
    with open(output_path, 'wb') as f:
        pickle.dump(samples, f)
    
    print(f"Saved {len(samples)} samples to {output_path}")

def save_metadata(metadata: Dict, output_path: str):
    """保存元数据"""
    with open(output_path, 'w') as f:
        json.dump(metadata, f, indent=2)

def main():
    parser = argparse.ArgumentParser(description='Preprocess videos for UniVLA Latent Action Model')
    parser.add_argument('--data_dir', type=str, default='data', 
                       help='Directory containing video files')
    parser.add_argument('--output_dir', type=str, default='processed_data',
                       help='Output directory for processed data')
    parser.add_argument('--train_ratio', type=float, default=0.8,
                       help='Ratio of training data')
    parser.add_argument('--resolution', type=int, nargs=2, default=[224, 224],
                       help='Target resolution (height width)')
    
    args = parser.parse_args()
    
    print("🎬 UniVLA Latent Action Model 数据预处理")
    print("=" * 50)
    
    # 初始化预处理器
    preprocessor = VideoPreprocessor(target_resolution=tuple(args.resolution))
    
    # 收集视频文件
    print(f"📁 从 {args.data_dir} 收集视频文件...")
    video_files = collect_video_files(args.data_dir)
    print(f"找到 {len(video_files)} 个视频文件")
    
    # 创建数据集分割
    train_files, val_files = create_dataset_split(video_files, args.train_ratio)
    print(f"训练集: {len(train_files)} 个文件")
    print(f"验证集: {len(val_files)} 个文件")
    
    # 处理训练数据
    print("\n🔄 处理训练数据...")
    train_samples = []
    for video_path in tqdm(train_files, desc="Processing train videos"):
        sample = preprocessor.process_single_video(video_path)
        if sample is not None:
            train_samples.append(sample)
    
    # 处理验证数据
    print("\n🔄 处理验证数据...")
    val_samples = []
    for video_path in tqdm(val_files, desc="Processing val videos"):
        sample = preprocessor.process_single_video(video_path)
        if sample is not None:
            val_samples.append(sample)
    
    # 保存处理后的数据
    os.makedirs(args.output_dir, exist_ok=True)
    
    train_output_path = os.path.join(args.output_dir, 'train_data.pkl')
    val_output_path = os.path.join(args.output_dir, 'val_data.pkl')
    
    save_processed_data(train_samples, train_output_path)
    save_processed_data(val_samples, val_output_path)
    
    # 创建和保存元数据
    metadata = {
        'total_videos': len(video_files),
        'train_samples': len(train_samples),
        'val_samples': len(val_samples),
        'target_resolution': args.resolution,
        'task_descriptions': preprocessor.task_descriptions,
        'dataset_statistics': {
            'segmented_videos_ok': len([f for f in video_files if 'segmented_videos_ok' in f]),
            'segmented_videos_nok_apperence': len([f for f in video_files if 'segmented_videos_nok_apperence' in f]),
            'segmented_videos_nok_ele': len([f for f in video_files if 'segmented_videos_nok_ele' in f])
        }
    }
    
    metadata_path = os.path.join(args.output_dir, 'metadata.json')
    save_metadata(metadata, metadata_path)
    
    print(f"\n✅ 数据预处理完成!")
    print(f"📊 统计信息:")
    print(f"   - 总视频数: {metadata['total_videos']}")
    print(f"   - 训练样本: {metadata['train_samples']}")
    print(f"   - 验证样本: {metadata['val_samples']}")
    print(f"   - 输出目录: {args.output_dir}")

if __name__ == "__main__":
    main()
