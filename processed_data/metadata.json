{"total_videos": 236, "train_samples": 188, "val_samples": 48, "target_resolution": [224, 224], "task_descriptions": {"segmented_videos_ok": "put the qualified product into the blue box", "segmented_videos_nok_apperence": "put the unqualified product with appearance defacts into the lower yellow box", "segmented_videos_nok_ele": "put the unqualified product with electric defacts into the upper yellow box"}, "dataset_statistics": {"segmented_videos_ok": 227, "segmented_videos_nok_apperence": 7, "segmented_videos_nok_ele": 2}}