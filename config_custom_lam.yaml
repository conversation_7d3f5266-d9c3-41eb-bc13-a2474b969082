# 自定义LAM训练配置文件
# 基于UniVLA的LAM配置，适配您的质量检测视频数据

model:
  image_channels: 3
  
  # LAM模型参数 - 与预训练模型保持一致
  lam_model_dim: 768
  lam_latent_dim: 128
  lam_num_latents: 16
  lam_patch_size: 14
  lam_enc_blocks: 12
  lam_dec_blocks: 12
  lam_num_heads: 12
  
  # VQ-VAE损失权重
  vq_beta: 0.25
  
  # 日志设置
  log_interval: 1000
  log_path: ./logs/custom_lam
  
  # 优化器设置
  optimizer:
    class_path: torch.optim.AdamW
    init_args:
      lr: 1e-4
      weight_decay: 1e-2
  
  # 任务名称
  task_name: custom_quality_inspection_lam
  stage: stage-1  # 从stage-1开始训练
  
  # 如果要进行stage-2训练，取消注释下面的行并设置正确的检查点路径
  # stage: stage-2
  # stage_one_ckpt: ./logs/custom_lam_stage1/epoch=0-step=10000.ckpt

# 数据配置
data:
  # 自定义数据路径
  train_data_path: ./processed_data/train_data.pkl
  val_data_path: ./processed_data/val_data.pkl
  
  # 批处理设置
  batch_size: 16  # 根据GPU内存调整
  
  # 图像设置
  resolution: 224
  num_frames: 2  # LAM使用2帧（初始帧+目标帧）
  
  # 数据增强
  image_aug: true
  
  # 工作进程数
  num_workers: 4

# 训练器配置
trainer:
  # 训练轮数 - 根据数据量调整
  max_epochs: 50
  
  # 硬件设置
  accelerator: gpu
  devices: 1  # 单GPU训练，如有多GPU可调整
  
  # 训练策略
  strategy: auto  # 单GPU时使用auto
  
  # 精度设置
  precision: 16-mixed  # 混合精度训练节省内存
  
  # 日志频率
  log_every_n_steps: 100
  
  # 梯度裁剪
  gradient_clip_val: 0.1
  
  # 检查点保存
  callbacks:
    - class_path: lightning.pytorch.callbacks.ModelCheckpoint
      init_args:
        dirpath: ./logs/custom_quality_inspection_lam
        filename: 'custom-lam-{epoch:02d}-{step:06d}'
        verbose: true
        save_last: true
        save_top_k: 3
        monitor: 'val_loss'
        mode: 'min'
        every_n_train_steps: 2000  # 每2000步保存一次
    
    # 早停回调
    - class_path: lightning.pytorch.callbacks.EarlyStopping
      init_args:
        monitor: 'val_loss'
        patience: 10
        mode: 'min'
        verbose: true
    
    # 学习率监控
    - class_path: lightning.pytorch.callbacks.LearningRateMonitor
      init_args:
        logging_interval: 'step'

# 日志记录
logger:
  - class_path: lightning.pytorch.loggers.TensorBoardLogger
    init_args:
      save_dir: ./logs
      name: custom_quality_inspection_lam
      version: null
      default_hp_metric: false
  
  # 如果要使用Weights & Biases，取消注释下面的配置
  # - class_path: lightning.pytorch.loggers.WandbLogger
  #   init_args:
  #     project: custom-lam-training
  #     name: quality-inspection-lam
  #     save_dir: ./logs
