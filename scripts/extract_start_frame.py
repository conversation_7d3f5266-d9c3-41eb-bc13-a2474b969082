#!/usr/bin/env python3
"""
视频帧提取脚本 - 基于模板图片相似度提取最佳帧

功能描述：
- 从视频中提取与模板图片最相似的帧
- 使用DINOv2预训练视觉编码器进行特征提取和相似度计算
- 对每个视频均匀提取30帧，选择相似度最高的帧保存

输入路径：
- 模板图片目录：data/template_images/first_frames
- 视频文件目录：data/short_videos/
  
输出路径：
- 提取的帧保存到：data/shotted_images/

技术特点：
- 使用DINOv2-base模型进行特征提取
- 计算余弦相似度进行帧选择
- 支持多种视频格式
- 包含进度显示和错误处理

作者：AI Assistant
日期：2025-07-30
"""

import os
import cv2
import torch
import numpy as np
from PIL import Image
import torch.nn.functional as F
from transformers import AutoImageProcessor, AutoModel
from pathlib import Path
import argparse
from tqdm import tqdm
import logging
from typing import Dict, List, Tuple, Optional
import warnings

# 忽略一些不重要的警告
warnings.filterwarnings("ignore", category=UserWarning)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('frame_extraction.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class FrameExtractor:
    """视频帧提取器类"""
    
    def __init__(self, device: str = 'auto'):
        """
        初始化帧提取器
        
        Args:
            device: 计算设备 ('cuda', 'cpu', 'auto')
        """
        # 设备选择
        if device == 'auto':
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        logger.info(f"使用设备: {self.device}")
        
        # 加载DINOv2模型
        self.model_name = "facebook/dinov2-base"
        logger.info(f"加载模型: {self.model_name}")
        
        try:
            self.processor = AutoImageProcessor.from_pretrained(self.model_name)
            self.model = AutoModel.from_pretrained(self.model_name)
            self.model.to(self.device)
            self.model.eval()
            logger.info("模型加载成功")
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
        
        # 模板图片特征缓存
        self.template_features = {}
        
    def extract_features(self, image: Image.Image) -> torch.Tensor:
        """
        提取图像特征向量
        
        Args:
            image: PIL图像对象
            
        Returns:
            特征向量张量
        """
        try:
            # 预处理图像
            inputs = self.processor(images=image, return_tensors="pt")
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # 提取特征
            with torch.no_grad():
                outputs = self.model(**inputs)
                # 使用CLS token作为全局特征
                features = outputs.last_hidden_state[:, 0]  # [1, 768]
                # L2归一化
                features = F.normalize(features, p=2, dim=1)
                
            return features
        except Exception as e:
            logger.error(f"特征提取失败: {e}")
            raise
    
    def load_template_features(self, template_dir: str) -> Dict[str, torch.Tensor]:
        """
        加载并缓存模板图片特征
        
        Args:
            template_dir: 模板图片目录路径
            
        Returns:
            模板特征字典
        """
        template_dir = Path(template_dir)
        
        if not template_dir.exists():
            raise FileNotFoundError(f"模板目录不存在: {template_dir}")
        
        logger.info("加载模板图片特征...")
        
        # 模板文件映射
        template_files = {
            'ok': 'ok.png',
            'nok_appearance': 'nok_appearance.png', 
            'nok_ele': 'nok_ele.png'
        }
        
        for template_type, filename in template_files.items():
            template_path = template_dir / filename
            
            if not template_path.exists():
                logger.warning(f"模板文件不存在: {template_path}")
                continue
                
            try:
                # 加载图像
                image = Image.open(template_path).convert('RGB')
                
                # 提取特征
                features = self.extract_features(image)
                self.template_features[template_type] = features
                
                logger.info(f"已加载模板: {template_type}")
                
            except Exception as e:
                logger.error(f"加载模板 {template_type} 失败: {e}")
                
        if not self.template_features:
            raise ValueError("没有成功加载任何模板图片")
            
        logger.info(f"共加载 {len(self.template_features)} 个模板")
        return self.template_features
    
    def extract_video_frames(self, video_path: str, num_frames: int = 30) -> List[Image.Image]:
        """
        从视频中均匀提取帧
        
        Args:
            video_path: 视频文件路径
            num_frames: 提取帧数
            
        Returns:
            图像帧列表
        """
        try:
            cap = cv2.VideoCapture(video_path)
            
            if not cap.isOpened():
                raise ValueError(f"无法打开视频文件: {video_path}")
            
            # 获取视频信息
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            if total_frames <= 0:
                raise ValueError(f"视频帧数无效: {total_frames}")
            
            # 计算采样间隔
            if total_frames <= num_frames:
                frame_indices = list(range(total_frames))
            else:
                frame_indices = np.linspace(0, total_frames - 1, num_frames, dtype=int)
            
            frames = []
            
            for frame_idx in frame_indices:
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                ret, frame = cap.read()
                
                if ret:
                    # 转换BGR到RGB
                    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    # 转换为PIL图像
                    pil_image = Image.fromarray(frame_rgb)
                    frames.append(pil_image)
            
            cap.release()
            
            if not frames:
                raise ValueError(f"未能从视频中提取任何帧: {video_path}")
            
            logger.debug(f"从视频 {video_path} 提取了 {len(frames)} 帧")
            return frames
            
        except Exception as e:
            logger.error(f"视频帧提取失败 {video_path}: {e}")
            raise
    
    def calculate_similarity(self, frame_features: torch.Tensor,
                           template_features: torch.Tensor) -> float:
        """
        计算特征向量之间的余弦相似度

        Args:
            frame_features: 帧特征向量
            template_features: 模板特征向量

        Returns:
            相似度分数 (0-1)
        """
        # 计算余弦相似度
        similarity = F.cosine_similarity(frame_features, template_features, dim=1)
        return similarity.item()

    def find_best_frame(self, video_path: str, template_type: str,
                       similarity_threshold: float = 0.8) -> Tuple[Image.Image, float]:
        """
        找到与指定模板最相似的视频帧

        Args:
            video_path: 视频文件路径
            template_type: 模板类型 ('ok', 'nok_appearance', 'nok_ele')
            similarity_threshold: 相似度阈值，低于此值的帧将被跳过

        Returns:
            (最佳帧图像, 相似度分数)
        """
        if template_type not in self.template_features:
            raise ValueError(f"未找到模板类型: {template_type}")

        # 提取视频帧
        frames = self.extract_video_frames(video_path)

        # 获取模板特征
        template_features = self.template_features[template_type]

        best_frame = None
        best_similarity = -1.0

        logger.debug(f"开始处理 {len(frames)} 帧，相似度阈值: {similarity_threshold:.3f}")

        # 遍历所有帧，计算相似度
        for i, frame in enumerate(frames):
            try:
                # 提取帧特征
                frame_features = self.extract_features(frame)

                # 计算相似度
                similarity = self.calculate_similarity(frame_features, template_features)

                # 只考虑超过阈值的帧
                if similarity >= similarity_threshold and similarity > best_similarity:
                    best_similarity = similarity
                    best_frame = frame

            except Exception as e:
                logger.warning(f"处理第 {i} 帧时出错: {e}")
                continue

        if best_frame is None:
            raise ValueError(f"未能找到相似度超过 {similarity_threshold:.3f} 的帧: {video_path}")

        logger.debug(f"最佳相似度: {best_similarity:.4f}")
        return best_frame, best_similarity

    def determine_video_type(self, video_path: str) -> str:
        """
        根据视频路径确定对应的模板类型

        Args:
            video_path: 视频文件路径

        Returns:
            模板类型字符串
        """
        video_path = str(video_path).lower()

        if 'segmented_videos_ok' in video_path:
            return 'ok'
        elif 'segmented_videos_nok_apperence' in video_path:
            return 'nok_appearance'
        elif 'segmented_videos_nok_ele' in video_path:
            return 'nok_ele'
        else:
            # 默认返回ok类型
            logger.warning(f"无法确定视频类型，使用默认类型 'ok': {video_path}")
            return 'ok'

    def process_videos(self, video_dir: str, output_dir: str,
                      similarity_threshold: float = 0.8) -> Dict[str, int]:
        """
        批量处理视频文件

        Args:
            video_dir: 视频目录路径
            output_dir: 输出目录路径
            similarity_threshold: 相似度阈值，低于此值的视频将被跳过

        Returns:
            处理统计信息
        """
        video_dir = Path(video_dir)
        output_dir = Path(output_dir)

        if not video_dir.exists():
            raise FileNotFoundError(f"视频目录不存在: {video_dir}")

        # 创建输出目录结构
        output_dir.mkdir(parents=True, exist_ok=True)
        for subdir in ['ok', 'nok_appearance', 'nok_ele']:
            (output_dir / subdir).mkdir(exist_ok=True)

        # 收集所有视频文件
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv'}
        video_files = []

        for root, dirs, files in os.walk(video_dir):
            for file in files:
                if Path(file).suffix.lower() in video_extensions:
                    video_files.append(Path(root) / file)

        if not video_files:
            raise ValueError(f"在目录中未找到视频文件: {video_dir}")

        logger.info(f"找到 {len(video_files)} 个视频文件")
        logger.info(f"使用相似度阈值: {similarity_threshold:.3f}")

        # 处理统计
        stats = {
            'success': 0,
            'failed': 0,
            'skipped_low_similarity': 0,
            'total': len(video_files)
        }

        # 批量处理
        for video_path in tqdm(video_files, desc="处理视频"):
            try:
                # 确定视频类型
                template_type = self.determine_video_type(video_path)

                # 找到最佳帧
                best_frame, similarity = self.find_best_frame(
                    str(video_path), template_type, similarity_threshold
                )

                # 生成输出文件名
                video_name = video_path.stem
                output_filename = f"{video_name}.jpg"
                output_path = output_dir / template_type / output_filename

                # 保存图像
                best_frame.save(output_path, 'JPEG', quality=95)

                logger.info(f"已保存: {output_path} (相似度: {similarity:.4f})")
                stats['success'] += 1

            except ValueError as e:
                if "未能找到相似度超过" in str(e):
                    logger.warning(f"跳过低相似度视频 {video_path}: {e}")
                    stats['skipped_low_similarity'] += 1
                else:
                    logger.error(f"处理视频失败 {video_path}: {e}")
                    stats['failed'] += 1
                continue
            except Exception as e:
                logger.error(f"处理视频失败 {video_path}: {e}")
                stats['failed'] += 1
                continue

        return stats


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='从视频中提取与模板图片最相似的帧')
    parser.add_argument('--template_dir', type=str,
                       default='data/template_images',
                       help='模板图片目录路径')
    parser.add_argument('--video_dir', type=str,
                       default='data/short_videos',
                       help='视频文件目录路径')
    parser.add_argument('--output_dir', type=str,
                       default='data/shotted_images',
                       help='输出目录路径')
    parser.add_argument('--device', type=str, choices=['cuda', 'cpu', 'auto'],
                       default='auto', help='计算设备')
    parser.add_argument('--num_frames', type=int, default=50,
                       help='每个视频提取的帧数')
    parser.add_argument('--similarity_threshold', type=float, default=0.85,
                       help='相似度阈值 (0.0-1.0)，低于此值的视频将被跳过')

    args = parser.parse_args()

    try:
        # 初始化提取器
        extractor = FrameExtractor(device=args.device)

        # 加载模板特征
        extractor.load_template_features(args.template_dir)

        # 处理视频
        stats = extractor.process_videos(
            args.video_dir,
            args.output_dir,
            args.similarity_threshold
        )

        # 输出统计信息
        logger.info("=" * 50)
        logger.info("处理完成!")
        logger.info(f"总计视频: {stats['total']}")
        logger.info(f"成功处理: {stats['success']}")
        logger.info(f"处理失败: {stats['failed']}")
        logger.info(f"低相似度跳过: {stats['skipped_low_similarity']}")
        logger.info(f"成功率: {stats['success']/stats['total']*100:.1f}%")
        logger.info(f"有效率: {stats['success']/(stats['total']-stats['skipped_low_similarity'])*100:.1f}%" if stats['total'] > stats['skipped_low_similarity'] else "有效率: N/A")
        logger.info("=" * 50)

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
